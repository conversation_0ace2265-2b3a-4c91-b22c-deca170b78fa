'use client';

import { InteractiveTutorial } from '@/components/onboarding';
import {
	<PERSON><PERSON>,
	<PERSON>,
	CardContent,
	CardHeader,
	ErrorDisplay,
	NavigationCard,
	Translate,
} from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import {
	CheckCircle2,
	Edit3,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { CollectionStatsCard } from './collection-stats-card';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Navigation Grid Section Skeleton */}
			<div className="space-y-4 sm:space-y-6">
				<div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
					<div className="h-8 w-48 bg-muted rounded animate-pulse" />
					<div className="hidden sm:block h-px flex-1 bg-muted animate-pulse" />
					<div className="h-10 w-32 bg-muted rounded animate-pulse" />
				</div>

				{/* Navigation Cards Grid Skeleton */}
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
					{Array.from({ length: 7 }).map((_, i) => (
						<Card key={i} className="relative overflow-hidden h-48">
							<div className="absolute inset-0 bg-gradient-to-br from-muted/20 to-muted/10 animate-pulse" />
							<CardContent className="relative z-20 p-6 h-full flex flex-col">
								{/* Icon skeleton */}
								<div className="mb-4">
									<div className="w-12 h-12 bg-muted rounded-xl animate-pulse" />
								</div>
								{/* Content skeleton */}
								<div className="flex-1 space-y-2">
									<div className="h-6 w-32 bg-muted rounded animate-pulse" />
									<div className="h-4 w-full bg-muted/70 rounded animate-pulse" />
									<div className="h-4 w-3/4 bg-muted/70 rounded animate-pulse" />
								</div>
								{/* Action skeleton */}
								<div className="mt-4 flex justify-end">
									<div className="h-8 w-8 bg-muted rounded animate-pulse" />
								</div>
							</CardContent>
						</Card>
					))}
				</div>
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();
	const { t } = useTranslation();

	// Tutorial state
	const [showTutorial, setShowTutorial] = useState(false);

	const wordCount = currentCollection?.word_ids.length || 0;

	const handleTutorialComplete = () => {
		setShowTutorial(false);
		if (currentCollection) {
			localStorage.setItem(`tutorial-completed-${currentCollection.id}`, 'true');
		}
	};

	const getStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Vocabulary step
				return 'available';
			case 2: // Paragraph step
				return 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Add words
				return 'available';
			case 2: // Browse words
				return wordCount === 0 ? 'locked' : 'available';
			case 3: // Study flashcards
				return wordCount === 0 ? 'locked' : 'available';
			case 4: // Take quiz
				return wordCount < 10 ? 'locked' : 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepLockedReason = (stepNumber: number): string | undefined => {
		switch (stepNumber) {
			case 2: // Browse words
				return wordCount === 0 ? t('collections.vocab.step2_locked_reason') : undefined;
			case 3: // Study flashcards
				return wordCount === 0 ? t('collections.vocab.step3_locked_reason') : undefined;
			case 4: // Take quiz
				return wordCount < 10 ? t('collections.vocab.step4_locked_reason') : undefined;
			default:
				return undefined;
		}
	};

	// All navigation options in a single array for grid layout
	const navigationOptions = currentCollection
		? [
				{
					title: t('collections.vocab.step1_title'),
					description: t('collections.vocab.step1_message'),
					icon: Zap,
					status: getVocabStepStatus(1),
					href: `/collections/${currentCollection.id}/vocabulary/generate`,
					lockedReason: getVocabStepLockedReason(1),
					gradient: 'from-yellow-500/10 to-orange-500/5',
				},
				{
					title: t('collections.vocab.step2_title'),
					description: t('collections.vocab.step2_message'),
					icon: ListChecks,
					status: getVocabStepStatus(2),
					href: `/collections/${currentCollection.id}/vocabulary/my-words`,
					lockedReason: getVocabStepLockedReason(2),
					gradient: 'from-blue-500/10 to-cyan-500/5',
				},
				{
					title: t('collections.vocab.step3_title'),
					description: t('collections.vocab.step3_message'),
					icon: RefreshCw,
					status: getVocabStepStatus(3),
					href: `/collections/${currentCollection.id}/vocabulary/review`,
					lockedReason: getVocabStepLockedReason(3),
					gradient: 'from-green-500/10 to-emerald-500/5',
				},
				{
					title: t('collections.vocab.step4_title'),
					description: t('collections.vocab.step4_message'),
					icon: Target,
					status: getVocabStepStatus(4),
					href: `/collections/${currentCollection.id}/vocabulary/mcq`,
					lockedReason: getVocabStepLockedReason(4),
					gradient: 'from-purple-500/10 to-violet-500/5',
				},
				{
					title: t('collections.tabs.paragraph_practice'),
					description: t('collections.overview.paragraph_practice_desc'),
					icon: Edit3,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/paragraph-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
					gradient: 'from-orange-500/10 to-red-500/5',
				},
				{
					title: t('qa_practice.tab_title'),
					description: t('collections.overview.qa_practice_desc'),
					icon: MessageSquare,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/qa-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
					gradient: 'from-pink-500/10 to-rose-500/5',
				},
				{
					title: t('collections.tabs.grammar_practice'),
					description: t('collections.overview.grammar_practice_desc'),
					icon: CheckCircle2,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/grammar-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
					gradient: 'from-teal-500/10 to-cyan-500/5',
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			{/* Interactive Tutorial */}
			<InteractiveTutorial
				isOpen={showTutorial}
				onClose={() => setShowTutorial(false)}
				onComplete={handleTutorialComplete}
			/>

			<div className="min-h-screen">
				<div className="w-full pb-4 sm:pb-6 lg:pb-8 space-y-4 sm:space-y-6 lg:space-y-8">
					<CollectionStatsCard collection={currentCollection} />

					{/* Navigation Grid Section */}
					<div className="space-y-4 sm:space-y-6">
						<div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
							<h2 className="text-xl sm:text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="hidden sm:block h-px flex-1 bg-gradient-to-r from-border to-transparent" />
							<Link href={`/collections/${currentCollection.id}/stats`}>
								<Button
									variant="outline"
									size="sm"
									className="flex items-center gap-2 w-full sm:w-auto"
								>
									<TrendingUp className="h-4 w-4" />
									<Translate text="collections.stats.title" />
								</Button>
							</Link>
						</div>

						{/* Navigation Cards Grid */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{navigationOptions.map((option, index) => (
								<NavigationCard
									key={option.href}
									title={option.title}
									description={option.description}
									icon={option.icon}
									href={option.href}
									status={option.status}
									lockedReason={option.lockedReason}
									gradient={option.gradient}
									delay={index * 0.1}
								/>
							))}
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
