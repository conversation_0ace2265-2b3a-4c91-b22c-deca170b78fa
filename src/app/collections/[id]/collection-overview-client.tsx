'use client';

import {
	FlowStepCard,
	InteractiveTutorial,
	ParagraphStepCard,
	VocabularyStepCard,
} from '@/components/onboarding';
import { <PERSON>ton, Card, CardContent, CardHeader, ErrorDisplay, Translate } from '@/components/ui';
import { useTranslation } from '@/contexts';
import { useCollections } from '@/hooks';
import {
	CheckCircle2,
	Edit3,
	FileText,
	GraduationCap,
	ListChecks,
	MessageSquare,
	RefreshCw,
	Target,
	TrendingUp,
	Zap,
} from 'lucide-react';
import Link from 'next/link';
import { useState } from 'react';
import { CollectionStatsCard } from './collection-stats-card';

function CollectionOverviewSkeleton() {
	return (
		<div className="container mx-auto py-8 space-y-8">
			{/* Collection Info Card Skeleton */}
			<div className="relative overflow-hidden">
				<div className="absolute inset-0 bg-gradient-to-br from-muted/30 via-muted/50 to-muted/30 rounded-2xl animate-pulse" />
				<Card className="relative border-muted/50 bg-background/80 backdrop-blur-sm">
					<CardHeader className="pb-4">
						<div className="flex items-center gap-4">
							<div className="p-2 rounded-xl bg-muted/50 animate-pulse">
								<div className="h-8 w-8 bg-muted rounded" />
							</div>
							<div className="h-8 w-80 bg-muted rounded-lg animate-pulse" />
						</div>
					</CardHeader>
					<CardContent>
						<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
							{[...Array(5)].map((_, i) => (
								<div
									key={i}
									className="p-4 rounded-xl bg-muted/30 border border-muted/50 animate-pulse"
								>
									<div className="flex items-center gap-3">
										<div className="p-2 rounded-lg bg-muted/50">
											<div className="h-5 w-5 bg-muted rounded" />
										</div>
										<div className="flex-1 space-y-2">
											<div className="h-3 w-20 bg-muted rounded" />
											<div className="h-4 w-24 bg-muted rounded" />
										</div>
									</div>
								</div>
							))}
						</div>
					</CardContent>
				</Card>
			</div>

			{/* Feature Cards Skeleton */}
			<div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-8">
				{[...Array(3)].map((_, i) => (
					<Card
						key={i}
						className="h-80 border-muted/50 bg-gradient-to-br from-background to-muted/20 animate-pulse"
					>
						<CardHeader className="pb-4">
							<div className="flex items-center justify-between">
								<div className="flex items-center gap-3">
									<div className="p-2 rounded-xl bg-muted/50">
										<div className="h-6 w-6 bg-muted rounded" />
									</div>
									<div className="h-6 w-32 bg-muted rounded" />
								</div>
								<div className="h-4 w-4 bg-muted rounded" />
							</div>
							<div className="h-4 w-48 bg-muted rounded mt-3" />
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{[...Array(4)].map((_, j) => (
									<div
										key={j}
										className="flex items-center gap-3 p-3 rounded-lg bg-muted/30"
									>
										<div className="h-4 w-4 bg-muted rounded" />
										<div className="h-4 w-32 bg-muted rounded" />
										<div className="ml-auto h-4 w-4 bg-muted rounded" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>
				))}
			</div>
		</div>
	);
}

export function CollectionOverviewClient() {
	const { currentCollection, loading, error, setError } = useCollections();
	const { t } = useTranslation();

	// Tutorial state
	const [showTutorial, setShowTutorial] = useState(false);

	const wordCount = currentCollection?.word_ids.length || 0;

	const handleTutorialComplete = () => {
		setShowTutorial(false);
		if (currentCollection) {
			localStorage.setItem(`tutorial-completed-${currentCollection.id}`, 'true');
		}
	};

	const getStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Vocabulary step
				return 'available';
			case 2: // Paragraph step
				return 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepStatus = (stepNumber: number): 'available' | 'locked' => {
		switch (stepNumber) {
			case 1: // Add words
				return 'available';
			case 2: // Browse words
				return wordCount === 0 ? 'locked' : 'available';
			case 3: // Study flashcards
				return wordCount === 0 ? 'locked' : 'available';
			case 4: // Take quiz
				return wordCount < 10 ? 'locked' : 'available';
			default:
				return 'locked';
		}
	};

	const getVocabStepLockedReason = (stepNumber: number): string | undefined => {
		switch (stepNumber) {
			case 2: // Browse words
				return wordCount === 0 ? t('collections.vocab.step2_locked_reason') : undefined;
			case 3: // Study flashcards
				return wordCount === 0 ? t('collections.vocab.step3_locked_reason') : undefined;
			case 4: // Take quiz
				return wordCount < 10 ? t('collections.vocab.step4_locked_reason') : undefined;
			default:
				return undefined;
		}
	};

	// Word Management Steps (Generate and List)
	const wordManagementSteps = currentCollection
		? [
				{
					stepNumber: '1.1',
					titleKey: 'collections.vocab.step1_title',
					messageKey: 'collections.vocab.step1_message',
					icon: Zap,
					status: getVocabStepStatus(1),
					href: `/collections/${currentCollection.id}/vocabulary/generate`,
					lockedReason: getVocabStepLockedReason(1),
				},
				{
					stepNumber: '1.2',
					titleKey: 'collections.vocab.step2_title',
					messageKey: 'collections.vocab.step2_message',
					icon: ListChecks,
					status: getVocabStepStatus(2),
					href: `/collections/${currentCollection.id}/vocabulary/my-words`,
					lockedReason: getVocabStepLockedReason(2),
				},
		  ]
		: [];

	// Vocabulary Practice Steps (Review and MCQ)
	const vocabularyPracticeSteps = currentCollection
		? [
				{
					stepNumber: '2.1',
					titleKey: 'collections.vocab.step3_title',
					messageKey: 'collections.vocab.step3_message',
					icon: RefreshCw,
					status: getVocabStepStatus(3),
					href: `/collections/${currentCollection.id}/vocabulary/review`,
					lockedReason: getVocabStepLockedReason(3),
				},
				{
					stepNumber: '2.2',
					titleKey: 'collections.vocab.step4_title',
					messageKey: 'collections.vocab.step4_message',
					icon: Target,
					status: getVocabStepStatus(4),
					href: `/collections/${currentCollection.id}/vocabulary/mcq`,
					lockedReason: getVocabStepLockedReason(4),
				},
		  ]
		: [];

	// Paragraph Practice Steps
	const paragraphPracticeSteps = currentCollection
		? [
				{
					stepNumber: '3.1',
					titleKey: 'collections.tabs.paragraph_practice',
					messageKey: 'collections.overview.paragraph_practice_desc',
					icon: Edit3,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/paragraph-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
				},
				{
					stepNumber: '3.2',
					titleKey: 'qa_practice.tab_title',
					messageKey: 'collections.overview.qa_practice_desc',
					icon: MessageSquare,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/qa-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
				},
				{
					stepNumber: '3.3',
					titleKey: 'collections.tabs.grammar_practice',
					messageKey: 'collections.overview.grammar_practice_desc',
					icon: CheckCircle2,
					status: getStepStatus(2),
					href: `/collections/${currentCollection.id}/paragraph/grammar-practice`,
					lockedReason:
						wordCount < 10 ? t('collections.paragraph.locked_reason') : undefined,
				},
		  ]
		: [];

	const flowSteps = currentCollection
		? [
				{
					stepNumber: 1,
					titleKey: 'collections.flow.word_management_title',
					messageKey: 'collections.flow.word_management_message',
					icon: ListChecks,
					status: getStepStatus(1),
				},
				{
					stepNumber: 2,
					titleKey: 'collections.flow.vocabulary_practice_title',
					messageKey: 'collections.flow.vocabulary_practice_message',
					icon: GraduationCap,
					status: getStepStatus(1),
				},
				{
					stepNumber: 3,
					titleKey: 'collections.flow.paragraph_practice_title',
					messageKey: 'collections.flow.paragraph_practice_message',
					icon: FileText,
					status: getStepStatus(2),
				},
		  ]
		: [];

	// Show loading skeleton while collection is being set or loaded
	if (loading.setCurrent || loading.get || !currentCollection) {
		return <CollectionOverviewSkeleton />;
	}

	return (
		<>
			<ErrorDisplay error={error} onDismiss={() => setError(null)} />

			{/* Interactive Tutorial */}
			<InteractiveTutorial
				isOpen={showTutorial}
				onClose={() => setShowTutorial(false)}
				onComplete={handleTutorialComplete}
			/>

			<div className="min-h-screen">
				<div className="w-full pb-4 sm:pb-6 lg:pb-8 space-y-4 sm:space-y-6 lg:space-y-8">
					<CollectionStatsCard collection={currentCollection} />

					{/* Learning Flow Section */}
					<div className="space-y-4 sm:space-y-6">
						<div className="flex flex-col sm:flex-row sm:items-center gap-3 sm:gap-4">
							<h2 className="text-xl sm:text-2xl font-bold">
								<Translate text="collections.overview.features_title" />
							</h2>
							<div className="hidden sm:block h-px flex-1 bg-gradient-to-r from-border to-transparent" />
							<Link href={`/collections/${currentCollection.id}/stats`}>
								<Button
									variant="outline"
									size="sm"
									className="flex items-center gap-2 w-full sm:w-auto"
								>
									<TrendingUp className="h-4 w-4" />
									<Translate text="collections.stats.title" />
								</Button>
							</Link>
						</div>

						<div className="space-y-4 sm:space-y-6">
							{flowSteps.map((step) => (
								<FlowStepCard
									key={step.stepNumber}
									stepNumber={step.stepNumber}
									titleKey={step.titleKey}
									messageKey={step.messageKey}
									icon={step.icon}
									status={step.status}
								>
									{step.stepNumber === 1 ? (
										// Word Management section
										<div className="space-y-2 sm:space-y-3">
											{wordManagementSteps.map((wordStep) => (
												<VocabularyStepCard
													key={wordStep.stepNumber}
													stepNumber={wordStep.stepNumber}
													titleKey={wordStep.titleKey}
													messageKey={wordStep.messageKey}
													icon={wordStep.icon}
													status={wordStep.status}
													href={wordStep.href}
													lockedReason={wordStep.lockedReason}
												/>
											))}
										</div>
									) : step.stepNumber === 2 ? (
										// Vocabulary Practice section
										<div className="space-y-2 sm:space-y-3">
											{vocabularyPracticeSteps.map((practiceStep) => (
												<VocabularyStepCard
													key={practiceStep.stepNumber}
													stepNumber={practiceStep.stepNumber}
													titleKey={practiceStep.titleKey}
													messageKey={practiceStep.messageKey}
													icon={practiceStep.icon}
													status={practiceStep.status}
													href={practiceStep.href}
													lockedReason={practiceStep.lockedReason}
												/>
											))}
										</div>
									) : step.stepNumber === 3 ? (
										// Paragraph Practice section
										<div className="space-y-2 sm:space-y-3">
											{paragraphPracticeSteps.map((paragraphStep) => (
												<ParagraphStepCard
													key={paragraphStep.stepNumber}
													stepNumber={paragraphStep.stepNumber}
													titleKey={paragraphStep.titleKey}
													messageKey={paragraphStep.messageKey}
													icon={paragraphStep.icon}
													status={paragraphStep.status}
													href={paragraphStep.href}
													lockedReason={paragraphStep.lockedReason}
												/>
											))}
										</div>
									) : null}
								</FlowStepCard>
							))}
						</div>
					</div>
				</div>
			</div>
		</>
	);
}
