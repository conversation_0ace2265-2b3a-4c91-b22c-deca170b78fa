'use client';

import { <PERSON><PERSON>, Card, CardContent } from '@/components/ui';
import { motion } from 'framer-motion';
import { ArrowRight, Lock } from 'lucide-react';
import { LucideIcon } from 'lucide-react';
import Link from 'next/link';

interface NavigationCardProps {
	title: string;
	description: string;
	icon: LucideIcon;
	href: string;
	status: 'available' | 'locked';
	lockedReason?: string;
	gradient?: string;
	className?: string;
	delay?: number;
}

export function NavigationCard({
	title,
	description,
	icon: Icon,
	href,
	status,
	lockedReason,
	gradient = 'from-primary/10 to-primary/5',
	className = '',
	delay = 0,
}: NavigationCardProps) {
	const isLocked = status === 'locked';

	const cardContent = (
		<motion.div
			initial={{ opacity: 0, y: 20 }}
			animate={{ opacity: 1, y: 0 }}
			transition={{ duration: 0.5, delay }}
			className={className}
		>
			<Card
				className={`
					relative overflow-hidden transition-all duration-300 group h-36
					${isLocked ? 'opacity-75' : 'hover:shadow-lg hover:scale-105 cursor-pointer'}
					border border-border/50
				`}
			>
				{/* Background gradient */}
				<div
					className={`
						absolute inset-0 bg-gradient-to-br ${gradient}
						${isLocked ? 'opacity-20' : 'opacity-50 group-hover:opacity-70'}
						transition-opacity duration-300
					`}
				/>

				{/* Lock overlay */}
				{isLocked && (
					<div className="absolute inset-0 bg-background/80 backdrop-blur-md z-10 flex items-center justify-center">
						<div className="text-center space-y-2">
							<Lock className="h-6 w-6 text-muted-foreground mx-auto" />
							{lockedReason && (
								<p className="text-xs text-muted-foreground px-4 leading-tight">
									{lockedReason}
								</p>
							)}
						</div>
					</div>
				)}

				<CardContent
					className={`relative p-4 h-full flex flex-col ${
						isLocked ? 'z-0 opacity-40' : 'z-20'
					}`}
				>
					{/* Icon */}
					<div className="mb-3">
						<div
							className={`
								w-10 h-10 rounded-lg flex items-center justify-center
								${isLocked ? 'bg-muted' : 'bg-background/80 backdrop-blur-sm'}
								transition-all duration-300
								${!isLocked ? 'group-hover:scale-110' : ''}
							`}
						>
							<Icon
								className={`
									h-5 w-5
									${isLocked ? 'text-muted-foreground' : 'text-primary'}
								`}
							/>
						</div>
					</div>

					{/* Content */}
					<div className="flex-1 space-y-1 min-h-0">
						<h3
							className={`
								font-semibold text-sm leading-tight line-clamp-2
								${isLocked ? 'text-muted-foreground' : 'text-foreground'}
							`}
						>
							{title}
						</h3>
						<p
							className={`
								text-xs leading-tight line-clamp-2
								${isLocked ? 'text-muted-foreground/70' : 'text-muted-foreground'}
							`}
						>
							{description}
						</p>
					</div>

					{/* Action */}
					{!isLocked && (
						<div className="mt-3 flex justify-end">
							<Button
								variant="ghost"
								size="sm"
								className="
									text-primary hover:text-primary-foreground hover:bg-primary
									transition-all duration-200 group-hover:translate-x-1 h-7 w-7 p-0
								"
							>
								<ArrowRight className="h-3 w-3" />
							</Button>
						</div>
					)}
				</CardContent>
			</Card>
		</motion.div>
	);

	if (isLocked) {
		return cardContent;
	}

	return (
		<Link href={href} className="block h-full">
			{cardContent}
		</Link>
	);
}
